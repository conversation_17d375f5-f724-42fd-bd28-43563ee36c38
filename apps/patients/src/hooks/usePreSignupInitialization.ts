import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { onboardingDataAtom, onboardingVersionAtom } from '@/store/store';
import { useMutation } from '@tanstack/react-query';
import { useAtomValue, useSetAtom } from 'jotai';

import { apiClient } from '@willow/utils/api/client';

interface InitializeResponse {
  version: 'v1' | 'legacy-v1' | 'legacy-v2';
  initialized: boolean;
  currentState?: string;
  canTransition?: boolean;
  isComplete?: boolean;
  context?: any;
  events?: string[];
  stepName?: string;
  percentage?: number;
}

export const usePreSignupInitialization = () => {
  const router = useRouter();
  const setOnboardingData = useSetAtom(onboardingDataAtom);
  const setOnboardingVersion = useSetAtom(onboardingVersionAtom);
  const onboardingVersion = useAtomValue(onboardingVersionAtom);
  const hasStartedInit = useRef(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const {
    mutate: initializeOnboarding,
    isPending,
    isSuccess,
  } = useMutation<InitializeResponse>({
    mutationFn: () =>
      apiClient.post('/onboarding/initialize').then((res) => {
        console.log('3️⃣ /onboarding/initialize');
        return res.data;
      }),
    onSuccess: (data) => {
      console.log('Initialize response:', data);

      // Pre-signup should always use v1 flow, regardless of what the API returns
      // Legacy flow is only for existing users who already completed signup
      const effectiveVersion = 'v1';
      setOnboardingVersion(effectiveVersion);

      // Set initial onboarding data for v1 pre-signup flow
      setOnboardingData({
        state: data.currentState || 'preSignup.stateSelection',
        context: data.context || {
          productType: '',
          questionnaireCompleted: false,
          questionnaire: {},
        },
        events: data.events || [],
        stepName: data.stepName || 'Account Creation',
        percentage: data.percentage || 0,
      });

      setIsInitialized(true);
    },
    onError: () => {
      // Pre-signup should always use v1 flow, even on error
      // Legacy flow is only for existing users who already completed signup
      console.log('Initialize API failed, using v1 flow for pre-signup');
      setOnboardingVersion('v1');

      // Set default state for v1 pre-signup flow
      setOnboardingData({
        state: 'preSignup.stateSelection',
        context: {
          productType: '',
          questionnaireCompleted: false,
          questionnaire: {},
        },
        events: [],
        stepName: 'Account Creation',
        percentage: 0,
      });

      setIsInitialized(true);
    },
  });

  useEffect(() => {
    console.log('😎 use effect ');
    // Only initialize once if we don't have a version set yet
    if (!onboardingVersion && !hasStartedInit.current && !isPending) {
      hasStartedInit.current = true;
      initializeOnboarding();
    } else if (onboardingVersion) {
      // If we already have a version, we're initialized
      setIsInitialized(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onboardingVersion]);

  return {
    isInitialized: isInitialized || !!onboardingVersion,
    isLoading: isPending && !isSuccess && !isInitialized,
  };
};

import type { OnboardingData } from '@/data/types';
import { onboardingVersionAtom } from '@/store/store';
import { useMutation } from '@tanstack/react-query';
import { useAtomValue } from 'jotai';

import { apiClient } from '@willow/utils/api/client';

interface PreSignupEventPayload {
  event: string;
  value?: any;
}

interface InitializeResponse {
  version: 'v1' | 'legacy-v1' | 'legacy-v2';
  initialized: boolean;
  currentState?: string;
  canTransition?: boolean;
  isComplete?: boolean;
  context?: any;
  events?: string[];
  stepName?: string;
  percentage?: number;
}

interface PreSignupResponse {
  currentState: string;
  canTransition: boolean;
  isComplete: boolean;
  context?: any;
  stepName?: string;
  percentage?: number;
}

interface AuthenticatedResponse {
  accessToken: string;
  refreshToken: string;
  role: string;
  status: string;
  patientId: string;
  onboarding?: {
    state: any;
    context: any;
    stepName: string;
    percentage?: number;
  };
}

export const useOnboardingService = () => {
  const onboardingVersion = useAtomValue(onboardingVersionAtom);

  // Initialize onboarding
  const initialize = useMutation<InitializeResponse>({
    mutationFn: () =>
      apiClient.post('/onboarding/initialize').then((res) => {
        console.log('2️⃣ /onboarding/initialize');
        return res.data;
      }),
  });

  // Pre-signup state machine transitions (v1)
  const preSignupTransition = useMutation<
    PreSignupResponse,
    unknown,
    PreSignupEventPayload
  >({
    mutationFn: (data) =>
      apiClient.post('/onboarding/pre-signup', data).then((res) => res.data),
  });

  // Specific pre-signup endpoints
  const preSignupSetState = useMutation<
    PreSignupResponse,
    unknown,
    { state: string }
  >({
    mutationFn: (data) =>
      apiClient
        .post('/onboarding/pre-signup/set-state', data)
        .then((res) => res.data),
  });

  const preSignupName = useMutation<
    PreSignupResponse,
    unknown,
    { firstName: string; lastName: string }
  >({
    mutationFn: (data) =>
      apiClient
        .post('/onboarding/pre-signup/name', data)
        .then((res) => res.data),
  });

  const preSignupCreateAccount = useMutation<
    AuthenticatedResponse,
    unknown,
    {
      email: string;
      password: string;
      phone: string;
      getPromotionsSMS?: boolean;
      promoCoupon?: string;
      referralCode?: string;
    }
  >({
    mutationFn: (data) =>
      apiClient
        .post('/onboarding/pre-signup/create-account', data)
        .then((res) => res.data),
  });

  const preSignupWaitingList = useMutation<
    PreSignupResponse,
    unknown,
    { email: string; firstName?: string; lastName?: string }
  >({
    mutationFn: (data) =>
      apiClient
        .post('/onboarding/pre-signup/waiting-list', data)
        .then((res) => res.data),
  });

  // Pre-signup back endpoint (cookie-based)
  const preSignupBack = useMutation<PreSignupResponse>({
    mutationFn: () =>
      apiClient.post('/onboarding/pre-signup/back').then((res) => res.data),
  });

  // Legacy and post-signup transitions
  const questionnaireTransition = useMutation<
    { data: OnboardingData },
    unknown,
    PreSignupEventPayload
  >({
    mutationFn: (data) =>
      apiClient.post('/onboarding/questionnaire', data).then((res) => res),
  });

  // Unified transition handler for pre-signup
  const sendPreSignupEvent = (event: string, value?: any) => {
    return preSignupTransition.mutateAsync({ event, value });
  };

  // Unified transition handler for questionnaire
  const sendQuestionnaireEvent = (event: string, value?: any) => {
    return questionnaireTransition.mutateAsync({ event, value });
  };

  // Pre-signup navigation helpers
  const nextPreSignup = (value?: any) => sendPreSignupEvent('next', value);

  // Questionnaire navigation helpers
  const nextQuestionnaire = (value?: any) =>
    sendQuestionnaireEvent('next', value);
  const backQuestionnaire = () => sendQuestionnaireEvent('back');

  // Generic helpers that decide based on version
  const next = (value?: any) => {
    if (onboardingVersion === 'v1') {
      return nextPreSignup(value);
    }
    return nextQuestionnaire(value);
  };

  return {
    initialize,
    sendPreSignupEvent,
    sendQuestionnaireEvent,
    next,
    nextPreSignup,
    nextQuestionnaire,
    backQuestionnaire,
    preSignupTransition,
    questionnaireTransition,
    // Specific pre-signup methods
    preSignupSetState,
    preSignupName,
    preSignupCreateAccount,
    preSignupWaitingList,
    preSignupBack,
    isLoading:
      preSignupTransition.isPending ||
      questionnaireTransition.isPending ||
      preSignupSetState.isPending ||
      preSignupName.isPending ||
      preSignupCreateAccount.isPending ||
      preSignupWaitingList.isPending ||
      preSignupBack.isPending,
  };
};

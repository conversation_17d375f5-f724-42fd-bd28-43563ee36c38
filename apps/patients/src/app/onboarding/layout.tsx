'use client';

import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import HomeNavbar from '@/components/nav/HomeNavbar';
import OnboardingLayout from '@/components/onboarding/OnboardingLayout';
import { useAnalytics } from '@/context/AnalyticsContext';
import { onboardingVersionAtom } from '@/store/store';
import { useAtomValue } from 'jotai';

const UnifiedOnboardingLayout = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const pathname = usePathname();
  const analytics = useAnalytics();
  const onboardingVersion = useAtomValue(onboardingVersionAtom);

  useEffect(() => {
    analyticsScreenViewed();
  }, [pathname]);

  const analyticsScreenViewed = () => {
    if (!analytics) return;
    void analytics.track('Onboarding Screen Viewed', {
      version: onboardingVersion,
      path: pathname,
    });
  };

  // For pre-signup flow, use the simpler layout with step counter
  const isPreSignup = pathname.includes('/pre-signup');

  if (isPreSignup) {
    return (
      <main className="relative flex min-h-screen flex-col">
        <HomeNavbar />
        <div className="flex flex-1 flex-grow flex-col">
          <OnboardingLayout withStepCounter={true}>{children}</OnboardingLayout>
        </div>
      </main>
    );
  }

  // For post-signup flow, use the existing connected layout structure
  return (
    <main className="relative flex min-h-screen flex-col bg-biege">
      <HomeNavbar />
      <div className="flex flex-1 flex-grow flex-col">{children}</div>
    </main>
  );
};

export default UnifiedOnboardingLayout;

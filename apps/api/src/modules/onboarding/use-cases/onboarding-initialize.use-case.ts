import { Injectable } from '@nestjs/common';
import { createActor } from 'xstate';

import type {
  AnyOnboardingSnapshot,
  GenericOnboardingCookie,
} from '../types/cookie.types';
import type { OnboardingInitializeResponse } from '../types/initialize-response.type';
import { OnboardingCookieService } from '../services/onboarding-cookie.service';
import { OnboardingStateService } from '../services/onboarding-state.service';
import {
  AnyOnboardingInput,
  ONBOARDING_MACHINE_REGISTRY,
} from '../states/versions';

@Injectable()
export class OnboardingInitializeUseCase {
  constructor(
    private readonly cookieService: OnboardingCookieService,
    private readonly onboardingStateService: OnboardingStateService,
  ) {}

  async execute(): Promise<OnboardingInitializeResponse> {
    // Initialize endpoint is only used for new patient pre-signup
    // Use the default version for new patients
    console.log('🆙 /onboarding/initialize');
    const version = this.onboardingStateService.getDefaultOnboardingVersion();

    // Check for existing cookie first
    const existingCookie = this.cookieService.getCookie();

    if (
      existingCookie &&
      existingCookie.version === version &&
      existingCookie.stateSnapshot
    ) {
      // Cookie exists with state machine data - restore the state
      // Get the full onboarding state using the state service
      const onboardingState =
        await this.onboardingStateService.getCurrentOnboardingState(
          version,
          existingCookie.stateSnapshot,
        );

      const currentState = this.getStateString(onboardingState.state);
      const isInPreSignup = currentState.startsWith('preSignup');

      return {
        version,
        initialized: true,
        currentState,
        canTransition: true,
        isComplete: !isInPreSignup,
        context: onboardingState.context,
        events: onboardingState.events,
        stepName: onboardingState.stepName,
        percentage: onboardingState.percentage,
      };
    }

    // No existing cookie or invalid state - create new state machine
    const versionedMachine = ONBOARDING_MACHINE_REGISTRY[version];
    const actor = createActor(versionedMachine, {
      input: {
        version,
        existingData: {},
      } as AnyOnboardingInput,
    });
    actor.start();
    const snapshot = actor.getSnapshot();

    // Get the full onboarding state using the state service
    const onboardingState =
      await this.onboardingStateService.getCurrentOnboardingState(
        version,
        snapshot,
      );

    const currentState = this.getStateString(onboardingState.state);

    // Create onboarding cookie with state snapshot
    const cookieData: GenericOnboardingCookie = {
      version,
      startedAt: new Date().toISOString(),
      currentState,
      stateSnapshot: actor.getPersistedSnapshot() as AnyOnboardingSnapshot,
    };

    // Set the cookie
    this.cookieService.setCookie(cookieData);

    return {
      version,
      initialized: true,
      currentState,
      canTransition: true,
      isComplete: false,
      context: onboardingState.context,
      events: onboardingState.events,
      stepName: onboardingState.stepName,
      percentage: onboardingState.percentage,
    };
  }

  private getStateString(value: any): string {
    if (typeof value === 'string') {
      return value;
    }
    if (typeof value === 'object' && value !== null) {
      const keys = Object.keys(value as Record<string, any>);
      if (keys.length === 1) {
        const key = keys[0];
        const nestedValue = value[key];
        if (typeof nestedValue === 'object') {
          return `${key}.${this.getStateString(nestedValue)}`;
        }
        return `${key}.${nestedValue}`;
      }
    }
    return String(value);
  }
}

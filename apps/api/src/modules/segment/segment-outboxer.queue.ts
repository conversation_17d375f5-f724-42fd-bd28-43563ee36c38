import { PrismaService } from '@modules/prisma/prisma.service';
import { JobMetadata, PgQueue } from '@modules/shared/queue/pg-queue.abstract';
import { SegmentIdentify, SegmentTrack } from '@modules/shared/types/events';
import { Injectable } from '@nestjs/common';
// import { Cron, CronExpression } from '@nestjs/schedule';
import { Treatment } from '@prisma/client';

import { SegmentAdapter } from './segment.adapter';

export type TreatmentUpdatedPayload = Treatment;

type SegmentQueuePayload =
  | {
      type: 'track';
      event: string;
      data: Omit<SegmentTrack, 'event' | 'userId' | 'anonymousId'>;
    }
  | {
      type: 'identify';
      data: Omit<SegmentIdentify, 'userId' | 'anonymousId'>;
    };

@Injectable()
export class SegmentOutboxerQueue extends PgQueue<SegmentQueuePayload> {
  constructor(
    private readonly segmentAdapter: SegmentAdapter,
    private readonly prisma: PrismaService,
  ) {
    super('segment.outboxer', {
      concurrency: 10,
      pollingIntervalSeconds: 0.5,
    });
  }

  protected work(payload: SegmentQueuePayload, { patientId }: JobMetadata) {
    if (payload.type === 'track') {
      return this.segmentAdapter.track({
        userId: patientId,
        event: payload.event,
        ...payload.data,
      });
    } else if (payload.type === 'identify') {
      return this.segmentAdapter.identify({
        userId: patientId,
        ...payload.data,
      });
    }
  }

  // @Cron(CronExpression.EVERY_HOUR)
  // async handleCron() {
  //   try {
  //     await this.prisma
  //       .$executeRaw`DELETE FROM "pgboss".job WHERE name = ${this.queueName} AND  "created_on" < NOW() - INTERVAL '5 hour' AND status = 'completed'`;
  //   } catch (error) {
  //     console.error('Error cleaning up completed jobs:', error.message);
  //   }
  // }
}

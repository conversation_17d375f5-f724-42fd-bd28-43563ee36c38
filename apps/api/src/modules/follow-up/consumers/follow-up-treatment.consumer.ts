import { PrismaService } from '@/modules/prisma/prisma.service';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { TreatmentUpdatedQueueEvent } from '@/modules/shared/events/treatment-topic.definition';
import {
  LoggerFactory,
  LoggerService,
} from '@/modules/shared/logger/logger.service';
import { OutboxerService } from '@/modules/shared/outboxer/outboxer.service';
import { FollowUpService } from '@modules/follow-up/services/follow-up.service';
import { CancelPatientFollowUpUseCase } from '@modules/follow-up/use-cases/cancel-patient-follow-up.use-case';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { TreatmentMachineContext } from '@modules/treatment/states/treatment.state';
import { Injectable, Logger } from '@nestjs/common';
import { subDays } from 'date-fns';

@Injectable()
export class FollowUpTreatmentConsumer {
  private logger: LoggerService;

  constructor(
    private readonly cancelPatientFollowUp: CancelPatientFollowUpUseCase,
    private readonly followUpService: FollowUpService,
    private readonly treatmentService: TreatmentService,
    private readonly prisma: PrismaService,
    private readonly loggerFactory: LoggerFactory,
    private readonly outboxer: OutboxerService,
  ) {
    this.logger = this.loggerFactory.createLogger(
      FollowUpTreatmentConsumer.name,
    );
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'follow-up-create-by-treatment-job',
    filter: ['created'],
  })
  async handleTreatmentCreateEvent({ payload }: TreatmentUpdatedQueueEvent) {
    const treatementState = payload.treatment.state as {
      context: TreatmentMachineContext;
    };

    const { context: treatmentContext } = treatementState;

    const { patientId, treatmentId, endOfLastRefillDate } = treatmentContext;

    // 1. check if treatment is core
    const isCore = treatmentContext.isCore;
    if (!isCore) return;

    // 2. get current active follow up for patient, if exists
    const existing = await this.followUpService.getActive(patientId);
    if (existing) {
      await this.cancelPatientFollowUp.execute(existing.id);
    }

    try {
      const scheduledAt = subDays(endOfLastRefillDate, 7);
      await this.followUpService.create(patientId, treatmentId, scheduledAt);
    } catch (e) {
      this.logger.error(
        e,
        { patientId },
        `[handleTreatmentCreateEvent] Error creating follow up for patient ${patientId}`,
      );
    }
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'follow-up-cancel-by-treatment-job',
    filter: ['cancelled', 'deleted'],
  })
  async handleFollowUpTreatmentCancelledEvent({
    payload,
  }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    const { patientId, treatmentId } = context;

    await this.prisma.$transaction(async (prisma) => {
      const isCore = await this.treatmentService.isCore(treatmentId, {
        prisma,
      });
      if (!isCore) return;

      const followUp = await this.followUpService.getActive(patientId, {
        prisma,
      });
      if (!followUp) return;

      if (followUp.treatmentId !== treatmentId) return;
      await this.cancelPatientFollowUp.execute(followUp.id, { prisma });
    });
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'follow-up-reschedule-by-treatement-job',
    filter: ['end_date_changed', 'resumed'],
  })
  async handleTreatmentEndDateChangedForFollowUpEvent({
    payload,
  }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    const { endOfLastRefillDate, patientId, treatmentId } = context;

    await this.prisma.$transaction(async (prisma) => {
      const isCore = await this.treatmentService.isCore(treatmentId, {
        prisma,
      });

      if (!isCore) return;

      // check if there is a scheduled follow up for this patient
      const followUp = await this.followUpService.getActive(patientId, {
        prisma,
      });

      if (!followUp) return;

      // check if the follow up is for the cancelled treatment
      if (followUp.treatmentId !== treatmentId) return;

      // check if the follow up is already scheduled at the new end of last refill date
      const scheduledAt = subDays(new Date(endOfLastRefillDate), 14);
      if (
        followUp.scheduledAt.getTime() ===
        new Date(endOfLastRefillDate).getTime()
      ) {
        this.logger.debug(
          `Follow up is already scheduled at the new end of last refill date`,
          { followUpId: followUp.id, scheduledAt },
        );
        return;
      }

      // shift the scheduled follow up
      await this.followUpService.moveScheduledAt(followUp.id, scheduledAt, {
        prisma,
      });
    });
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'follow-up-pause-by-treatment-job',
    filter: ['paused'],
  })
  async handleTreatmentPausedEvent({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    const { patientId, treatmentId } = context;

    await this.prisma.$transaction(async (prisma) => {
      const isCore = await this.treatmentService.isCore(treatmentId, {
        prisma,
      });
      if (!isCore) return;

      const followUp = await this.followUpService.getActive(patientId, {
        prisma,
      });
      if (!followUp) return;

      if (followUp.treatmentId !== treatmentId) return;

      // Cancel the follow-up
      await this.cancelPatientFollowUp.execute(followUp.id, { prisma });

      // Cancel any pending follow-up events in the outbox to prevent accidental execution
      await this.outboxer.cancelPendingFollowUpEvents(patientId, treatmentId, {
        prisma,
      });

      this.logger.log(
        `Follow-up cancelled and pending events cleared for paused treatment ${treatmentId} of patient ${patientId}`,
      );
    });
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'follow-up-resume-by-treatment-job',
    filter: ['resumed'],
  })
  async handleTreatmentResumedEvent({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    const { patientId, treatmentId, endOfLastRefillDate } = context;

    await this.prisma.$transaction(async (prisma) => {
      const isCore = await this.treatmentService.isCore(treatmentId, {
        prisma,
      });
      if (!isCore) return;

      // Check if there's already an active follow-up for this patient
      const existingFollowUp = await this.followUpService.getActive(patientId, {
        prisma,
      });

      // If there's an existing follow-up for a different treatment, cancel it
      if (existingFollowUp && existingFollowUp.treatmentId !== treatmentId) {
        await this.cancelPatientFollowUp.execute(existingFollowUp.id, {
          prisma,
        });
      }

      // If there's already a follow-up for this treatment, don't create a new one
      if (existingFollowUp && existingFollowUp.treatmentId === treatmentId) {
        this.logger.log(
          `Follow-up already exists for resumed treatment ${treatmentId} of patient ${patientId}`,
        );
        return;
      }

      try {
        // Create new follow-up scheduled 7 days before the end of last refill
        const scheduledAt = subDays(endOfLastRefillDate, 7);
        await this.followUpService.create(patientId, treatmentId, scheduledAt, {
          prisma,
        });

        this.logger.log(
          `New follow-up created for resumed treatment ${treatmentId} of patient ${patientId}, scheduled at ${scheduledAt.toISOString()}`,
        );
      } catch (e) {
        this.logger.error(
          e instanceof Error ? e : new Error(String(e)),
          { patientId, treatmentId },
          `[handleTreatmentResumedEvent] Error creating follow up for resumed treatment ${treatmentId} of patient ${patientId}`,
        );
      }
    });
  }
}

import confirm from '@inquirer/confirm';
import input from '@inquirer/input';
import search from '@inquirer/search';
import { PrismaService } from '@modules/prisma/prisma.service';
import { TreatmentService } from '@modules/treatment/services/treatment.service';
import { Injectable, Logger } from '@nestjs/common';
import * as chalk from 'chalk';
import { Command, CommandRunner, Option } from 'nest-commander';

interface TreatmentOption {
  id: string;
  status: string;
  productName: string;
  pharmacyName: string;
  dosageLabel?: string;
  currentRefill: number;
  refills: number;
  createdAt: Date;
  state: any;
  displayName: string;
}

@Injectable()
@Command({
  name: 'trigger-prescribe-failed',
  description:
    'Trigger prescribeFailed event for stuck treatments in prescribing state',
})
export class TriggerPrescribeFailedCommand extends CommandRunner {
  private readonly logger = new Logger(TriggerPrescribeFailedCommand.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly treatmentService: TreatmentService,
  ) {
    super();
  }

  @Option({
    flags: '--email [email]',
    description: 'Patient email (skips email input)',
  })
  parseEmail(val: string): string {
    return val;
  }

  @Option({
    flags: '--treatment-id [treatmentId]',
    description:
      'Treatment ID to trigger event for (skips treatment selection)',
  })
  parseTreatmentId(val: string): string {
    return val;
  }

  async run(
    _: string[],
    options?: {
      email?: string;
      treatmentId?: string;
    },
  ): Promise<void> {
    try {
      const isInteractive = !options?.email || !options?.treatmentId;

      // 1. Get patient by email
      const patientEmail = options?.email || (await this.getPatientEmail());
      const patient = await this.findPatientByEmail(patientEmail);

      if (!patient) {
        throw new Error(`Patient with email ${patientEmail} not found`);
      }

      console.log(
        chalk.green(
          `✅ Found patient: ${patient.user.firstName} ${patient.user.lastName}`,
        ),
      );

      // 2. Get treatments for patient
      const treatments = await this.getPatientTreatments(patient.id);

      if (treatments.length === 0) {
        console.log(chalk.yellow('⚠️  No treatments found for this patient.'));
        return;
      }

      // 3. Select treatment
      const selectedTreatment = options?.treatmentId
        ? treatments.find((t) => t.id === options.treatmentId)
        : await this.selectTreatment(treatments);

      if (!selectedTreatment) {
        throw new Error('Selected treatment not found');
      }

      // 4. Show confirmation
      if (isInteractive) {
        const confirmed = await this.showConfirmation(
          patient,
          selectedTreatment,
        );
        if (!confirmed) {
          console.log(chalk.yellow('Operation cancelled.'));
          return;
        }
      }

      // 5. Trigger prescribeFailed event
      await this.triggerPrescribeFailedEvent(selectedTreatment.id);

      console.log(
        chalk.green(
          `✅ Successfully triggered prescribeFailed event for treatment ${selectedTreatment.id}`,
        ),
      );

      // 6. Show non-interactive command if run interactively
      if (isInteractive) {
        this.showNonInteractiveCommand(patientEmail, selectedTreatment.id);
      }
    } catch (error) {
      console.error(chalk.red('Error:'), error.message);
      process.exit(1);
    }
  }

  private async getPatientEmail(): Promise<string> {
    const email = await input({
      message: 'Enter patient email:',
      validate: (input: string) => {
        if (!input?.trim()) {
          return 'Patient email is required';
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(input.trim())) {
          return 'Please enter a valid email address';
        }
        return true;
      },
    });

    return email.trim();
  }

  private async findPatientByEmail(email: string) {
    return await this.prismaService.patient.findFirst({
      where: {
        user: {
          email: email,
        },
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  private extractStatePath(value: any): string {
    if (typeof value === 'string') return value;
    if (typeof value === 'object' && value !== null) {
      const entries = Object.entries(value);
      if (entries.length === 1) {
        const [key, val] = entries[0];
        return val ? `${key}.${this.extractStatePath(val)}` : key;
      }
      return Object.keys(value).join('.');
    }
    return '';
  }

  private async getPatientTreatments(
    patientId: string,
  ): Promise<TreatmentOption[]> {
    const treatments = await this.prismaService.treatment.findMany({
      where: {
        patientId: patientId,
        deletedAt: null,
      },
      include: {
        initialProductPrice: {
          include: {
            product: {
              include: {
                pharmacy: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return treatments.map((treatment) => {
      const state = treatment.state as any;
      const productName = treatment.initialProductPrice.product.name;
      const pharmacyName = treatment.initialProductPrice.product.pharmacy.name;
      const dosageLabel = treatment.initialProductPrice.dosageLabel;

      // Extract current state for display
      const currentState = state?.value
        ? this.extractStatePath(state.value)
        : treatment.status;

      const displayName = `${productName} | Refill ${treatment.currentRefill}/${treatment.refills} | ${currentState}${dosageLabel ? ` | ${dosageLabel}` : ''} | Created: ${treatment.createdAt.toLocaleDateString()}`;

      return {
        id: treatment.id,
        status: treatment.status,
        productName,
        pharmacyName,
        dosageLabel,
        currentRefill: treatment.currentRefill,
        refills: treatment.refills,
        createdAt: treatment.createdAt,
        state,
        displayName,
      };
    });
  }

  private async selectTreatment(
    treatments: TreatmentOption[],
  ): Promise<TreatmentOption | null> {
    if (treatments.length === 1) {
      console.log(
        chalk.blue(`Found 1 treatment: ${treatments[0].displayName}`),
      );
      return treatments[0];
    }

    console.log(
      chalk.blue(`Found ${treatments.length} treatments for this patient.`),
    );

    const selectedTreatmentId = await search({
      message: 'Select a treatment (type to search):',
      source: async (input: string) => {
        const filtered = treatments.filter((option) =>
          option.displayName.toLowerCase().includes(input?.toLowerCase() || ''),
        );
        return filtered.map((option) => ({
          name: option.displayName,
          value: option.id,
        }));
      },
    });

    return treatments.find((t) => t.id === selectedTreatmentId) || null;
  }

  private async showConfirmation(
    patient: any,
    treatment: TreatmentOption,
  ): Promise<boolean> {
    console.log('\n' + chalk.cyan('=== CONFIRMATION ==='));
    console.log(
      `${chalk.bold('Patient:')} ${patient.user.firstName} ${patient.user.lastName} (${patient.user.email})`,
    );

    // Create a user-friendly treatment display
    const treatmentDisplay = `${treatment.productName} (Refill ${treatment.currentRefill}/${treatment.refills})`;
    console.log(`${chalk.bold('Treatment:')} ${treatmentDisplay}`);

    if (treatment.dosageLabel) {
      console.log(`${chalk.bold('Dosage:')} ${treatment.dosageLabel}`);
    }

    // Show the actual state machine state
    if (treatment.state?.value) {
      const currentState = this.extractStatePath(treatment.state.value);
      console.log(`${chalk.bold('Current State:')} ${currentState}`);
    } else {
      console.log(`${chalk.bold('Current Status:')} ${treatment.status}`);
    }

    console.log(
      `${chalk.bold('Created:')} ${treatment.createdAt.toLocaleDateString()}`,
    );

    console.log(
      '\n' +
        chalk.yellow(
          'This will trigger a prescribeFailed event, moving the treatment to waitingForPrescription state.',
        ),
    );
    console.log('\n');

    return await confirm({
      message: `Trigger prescribeFailed event for ${treatmentDisplay}?`,
      default: false,
    });
  }

  private async triggerPrescribeFailedEvent(
    treatmentId: string,
  ): Promise<void> {
    try {
      // Get the treatment actor
      const actor = await this.treatmentService.getActor(treatmentId, (e) =>
        this.logger.verbose(`[TriggerPrescribeFailedEvent] ${treatmentId}:`, e),
      );

      // Send the prescribeFailed event
      actor.send({ type: 'prescribeFailed' });

      // Update the treatment record
      await this.treatmentService.updateTreatmentRecord(actor);

      this.logger.log(
        `Triggered prescribeFailed event for treatment ${treatmentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to trigger prescribeFailed event for treatment ${treatmentId}:`,
        error,
      );
      throw error;
    }
  }

  private showNonInteractiveCommand(email: string, treatmentId: string): void {
    console.log('\n' + chalk.cyan('=== NON-INTERACTIVE COMMAND ==='));
    console.log(chalk.gray('To run this command non-interactively, use:'));
    console.log('');

    const command = `pnpm -F api cli trigger-prescribe-failed --email "${email}" --treatment-id "${treatmentId}"`;

    console.log(chalk.white(command));
    console.log('');
  }
}
